<?php

namespace App\Traits\Gateways;

use App\Models\AffiliateHistory;
use App\Models\Deposit;
use App\Models\Setting;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Models\Withdrawal;
use App\Notifications\NewDepositNotification;
use App\Helpers\Core;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

trait BsPayTrait
{
    protected static $clienteIdBsPay;
    protected static $clienteSecretBsPay;
    protected static $uriBsPay = 'https://ws.bspay.com.br/';

    /**
     * Generate credentials for BsPay
     */
    private static function generateCredentialsBsPay()
    {
        $setting = \Helper::getSetting();
        self::$clienteIdBsPay = $setting->bspay_client_id;
        self::$clienteSecretBsPay = $setting->bspay_client_secret;
    }

    /**
     * Request QR Code from BsPay
     */
    public static function requestQrcodeBsPay(Request $request)
    {
        try {
            self::generateCredentialsBsPay();
            
            if (empty(self::$clienteIdBsPay) || empty(self::$clienteSecretBsPay)) {
                Log::error('BsPay credentials not configured');
                return response()->json(['status' => false, 'message' => 'Gateway não configurado'], 400);
            }

            $idUnico = uniqid();
            
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . self::getTokenBsPay()
            ])->post(self::$uriBsPay . 'api/v1/pix/qrcode', [
                'external_id' => $idUnico,
                'amount' => (float) $request->input('amount'),
                'payer' => [
                    'name' => auth('api')->user()->name,
                    'document' => \Helper::soNumero($request->cpf),
                    'email' => auth('api')->user()->email
                ],
                'description' => 'Depósito via PIX',
                'callback_url' => url('/bspay/callback', [], true)
            ]);

            if ($response->successful()) {
                $responseData = $response->json();
                
                self::generateTransactionBsPay($responseData['transactionId'], $request->input('amount'), $idUnico);
                self::generateDepositBsPay($responseData['transactionId'], $request->input('amount'));
                
                Log::info('BsPay QR Code generated successfully', [
                    'transactionId' => $responseData['transactionId'],
                    'external_id' => $idUnico
                ]);
                
                return response()->json([
                    'status' => true, 
                    'idTransaction' => $responseData['transactionId'], 
                    'qrcode' => $responseData['qr_code'],
                    'pix_key' => $responseData['pix_key'] ?? null
                ]);
            } else {
                Log::error('BsPay QR Code generation failed', [
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);
                
                return response()->json([
                    'status' => false, 
                    'message' => 'Erro ao gerar QR Code'
                ], 400);
            }
            
        } catch (Exception $e) {
            Log::error('BsPay QR Code Error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return response()->json([
                'status' => false, 
                'message' => 'Erro interno do servidor'
            ], 500);
        }
    }

    /**
     * Get authentication token from BsPay
     */
    private static function getTokenBsPay()
    {
        try {
            $response = Http::post(self::$uriBsPay . 'oauth/token', [
                'grant_type' => 'client_credentials',
                'client_id' => self::$clienteIdBsPay,
                'client_secret' => self::$clienteSecretBsPay,
                'scope' => '*'
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['access_token'];
            }
            
            Log::error('BsPay token generation failed', [
                'status' => $response->status(),
                'response' => $response->json()
            ]);
            
            return null;
            
        } catch (Exception $e) {
            Log::error('BsPay Token Error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate transaction record
     */
    private static function generateTransactionBsPay($transactionId, $amount, $idUnico)
    {
        try {
            $transaction = Transaction::create([
                'payment_id' => $transactionId,
                'user_id' => auth('api')->id(),
                'payment_method' => 'pix',
                'price' => $amount,
                'currency' => 'BRL',
                'status' => 0,
                'idUnico' => $idUnico
            ]);
            
            Log::info('BsPay transaction created', [
                'transaction_id' => $transaction->id,
                'payment_id' => $transactionId
            ]);
            
            return $transaction;
            
        } catch (Exception $e) {
            Log::error('BsPay Transaction Creation Error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate deposit record
     */
    private static function generateDepositBsPay($transactionId, $amount)
    {
        try {
            $deposit = Deposit::create([
                'payment_id' => $transactionId,
                'user_id' => auth('api')->id(),
                'amount' => $amount,
                'type' => 'pix',
                'proof' => null,
                'status' => 0,
                'currency' => 'BRL',
                'symbol' => 'R$'
            ]);
            
            Log::info('BsPay deposit created', [
                'deposit_id' => $deposit->id,
                'payment_id' => $transactionId
            ]);
            
            return $deposit;
            
        } catch (Exception $e) {
            Log::error('BsPay Deposit Creation Error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Webhook handler for BsPay
     */
    public static function webhookBsPay(Request $request)
    {
        try {
            // Log dos dados recebidos para debug
            Log::info('BsPay Webhook received:', $request->all());
            
            $requestBody = $request->input("requestBody");
            
            // Verificar se requestBody existe e não é null
            if (!$requestBody || !is_array($requestBody)) {
                Log::warning('BsPay Webhook: requestBody is null or invalid', [
                    'request_data' => $request->all()
                ]);
                return response()->json(['error' => 'requestBody inválido'], 400);
            }
            
            // Verificar se os campos obrigatórios existem
            if (!isset($requestBody['transactionId']) || !isset($requestBody['external_id'])) {
                Log::warning('BsPay Webhook: Missing required fields', [
                    'requestBody' => $requestBody
                ]);
                return response()->json(['error' => 'Campos obrigatórios ausentes'], 400);
            }

            $idTransaction = $requestBody['transactionId'];
            $externalId = $requestBody['external_id'];
            
            // Verificar se a transação existe
            $transaction = Transaction::where('payment_id', $idTransaction)
                ->where('status', 0)
                ->first();
            
            if (!$transaction) {
                Log::warning('BsPay Webhook: Transaction not found', [
                    'transactionId' => $idTransaction
                ]);
                return response()->json(['error' => 'Transação não encontrada'], 404);
            }
            
            // Verificar se o external_id confere
            if ($transaction->idUnico != $externalId) {
                Log::warning('BsPay Webhook: External ID mismatch', [
                    'expected' => $transaction->idUnico,
                    'received' => $externalId
                ]);
                return response()->json(['error' => 'ID externo não confere'], 401);
            }

            // Processar o pagamento
            $payment = self::finalizePaymentBsPay($request);
            
            if ($payment) {
                Log::info('BsPay Webhook: Payment processed successfully', [
                    'transactionId' => $idTransaction
                ]);
                return response()->json(['status' => 'success'], 200);
            } else {
                Log::error('BsPay Webhook: Payment processing failed', [
                    'transactionId' => $idTransaction
                ]);
                return response()->json(['error' => 'Falha no processamento'], 500);
            }
            
        } catch (Exception $e) {
            Log::error('BsPay Webhook Error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'request_data' => $request->all()
            ]);
            return response()->json(['error' => 'Erro interno'], 500);
        }
    }

    /**
     * Finalize payment processing
     */
    private static function finalizePaymentBsPay(Request $request)
    {
        try {
            $requestBody = $request->input("requestBody");
            
            // Verificar se requestBody existe
            if (!$requestBody || !is_array($requestBody)) {
                Log::error('finalizePaymentBsPay: requestBody is null or invalid');
                return false;
            }
            
            // Verificar se transactionId existe
            if (!isset($requestBody['transactionId'])) {
                Log::error('finalizePaymentBsPay: transactionId not found in requestBody');
                return false;
            }

            $idTransaction = $requestBody['transactionId'];
            $transaction = Transaction::where('payment_id', $idTransaction)
                ->where('status', 0)
                ->first();

            if (!$transaction) {
                Log::error('finalizePaymentBsPay: Transaction not found', [
                    'transactionId' => $idTransaction
                ]);
                return false;
            }

            $user = User::find($transaction->user_id);
            if (!$user) {
                Log::error('finalizePaymentBsPay: User not found', [
                    'user_id' => $transaction->user_id
                ]);
                return false;
            }

            $wallet = Wallet::where('user_id', $transaction->user_id)->first();
            if (!$wallet) {
                Log::error('finalizePaymentBsPay: Wallet not found', [
                    'user_id' => $transaction->user_id
                ]);
                return false;
            }

            $setting = Setting::first();
            if (!$setting) {
                Log::error('finalizePaymentBsPay: Settings not found');
                return false;
            }

            // Verificar se é o primeiro depósito
            $checkTransactions = Transaction::where('user_id', $transaction->user_id)
                ->where('status', 1)
                ->count();

            if ($checkTransactions == 0) {
                // Pagar bônus de primeiro depósito
                $bonus = Core::porcentagem_xn($setting->initial_bonus, $transaction->price);
                $wallet->increment('balance_bonus', $bonus);
                $wallet->update(['balance_bonus_rollover' => $bonus * $setting->rollover]);
            }

            // Rollover depósito
            $wallet->update(['balance_deposit_rollover' => $transaction->price * intval($setting->rollover_deposit)]);

            // Acumular bônus VIP
            Core::payBonusVip($wallet, $transaction->price);

            // Incrementar saldo
            $wallet->increment('balance', $transaction->price);
            $transaction->update(['status' => 1]);

            $deposit = Deposit::where('payment_id', $idTransaction)->where('status', 0)->first();
            
            if ($deposit) {
                // Processar CPA se existir
                $affHistoryCPA = AffiliateHistory::where('user_id', $user->id)
                    ->where('commission_type', 'cpa')
                    ->where('status', 0)
                    ->first();

                if ($affHistoryCPA && $user->inviter) {
                    $sponsorCpa = User::find($user->inviter);
                    
                    if ($sponsorCpa) {
                        $deposited_amount = ($affHistoryCPA->deposited_amount ?? 0) + $transaction->price;
                        
                        if ($deposited_amount >= $sponsorCpa->affiliate_baseline) {
                            $walletCpa = Wallet::where('user_id', $affHistoryCPA->inviter)->first();
                            
                            if ($walletCpa) {
                                $walletCpa->increment('refer_rewards', $sponsorCpa->affiliate_cpa);
                                $affHistoryCPA->update([
                                    'status' => 1,
                                    'deposited_amount' => $deposited_amount,
                                    'commission_paid' => $sponsorCpa->affiliate_cpa
                                ]);
                            }
                        } else {
                            $affHistoryCPA->update(['deposited_amount' => $deposited_amount]);
                        }
                    }
                }

                $deposit->update(['status' => 1]);

                // Notificar admins
                $admins = User::where('role_id', 0)->get();
                foreach ($admins as $admin) {
                    $admin->notify(new NewDepositNotification($user->name, $transaction->price));
                }
            }

            Log::info('BsPay Payment finalized successfully', [
                'transactionId' => $idTransaction,
                'userId' => $user->id,
                'amount' => $transaction->price
            ]);

            return true;
            
        } catch (Exception $e) {
            Log::error('finalizePaymentBsPay Error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'request_data' => $request->all()
            ]);
            return false;
        }
    }

    /**
     * Consult transaction status
     */
    public static function consultStatusTransactionBsPay(Request $request)
    {
        try {
            self::generateCredentialsBsPay();
            
            $transactionId = $request->input('transaction_id');
            
            if (!$transactionId) {
                return response()->json([
                    'status' => false,
                    'message' => 'ID da transação é obrigatório'
                ], 400);
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . self::getTokenBsPay()
            ])->get(self::$uriBsPay . 'api/v1/transactions/' . $transactionId);

            if ($response->successful()) {
                $data = $response->json();
                
                return response()->json([
                    'status' => true,
                    'data' => $data
                ]);
            } else {
                Log::error('BsPay status consultation failed', [
                    'transactionId' => $transactionId,
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);
                
                return response()->json([
                    'status' => false,
                    'message' => 'Erro ao consultar status'
                ], 400);
            }
            
        } catch (Exception $e) {
            Log::error('BsPay Status Consultation Error: ' . $e->getMessage());
            
            return response()->json([
                'status' => false,
                'message' => 'Erro interno do servidor'
            ], 500);
        }
    }

    /**
     * Process withdrawal via BsPay
     */
    public static function withdrawalBsPay(Request $request)
    {
        try {
            self::generateCredentialsBsPay();
            
            $withdrawal = Withdrawal::find($request->input('withdrawal_id'));
            
            if (!$withdrawal) {
                return response()->json([
                    'status' => false,
                    'message' => 'Saque não encontrado'
                ], 404);
            }

            $user = User::find($withdrawal->user_id);
            
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Usuário não encontrado'
                ], 404);
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . self::getTokenBsPay()
            ])->post(self::$uriBsPay . 'api/v1/pix/transfer', [
                'external_id' => uniqid(),
                'amount' => (float) $withdrawal->amount,
                'recipient' => [
                    'name' => $user->name,
                    'document' => \Helper::soNumero($user->cpf),
                    'pix_key' => $withdrawal->pix_key,
                    'pix_key_type' => $withdrawal->pix_type
                ],
                'description' => 'Saque via PIX'
            ]);

            if ($response->successful()) {
                $responseData = $response->json();
                
                $withdrawal->update([
                    'payment_id' => $responseData['transactionId'],
                    'status' => 1
                ]);
                
                Log::info('BsPay withdrawal processed successfully', [
                    'withdrawal_id' => $withdrawal->id,
                    'transactionId' => $responseData['transactionId']
                ]);
                
                return response()->json([
                    'status' => true,
                    'message' => 'Saque processado com sucesso',
                    'transaction_id' => $responseData['transactionId']
                ]);
            } else {
                Log::error('BsPay withdrawal failed', [
                    'withdrawal_id' => $withdrawal->id,
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);
                
                return response()->json([
                    'status' => false,
                    'message' => 'Erro ao processar saque'
                ], 400);
            }
            
        } catch (Exception $e) {
            Log::error('BsPay Withdrawal Error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => 'Erro interno do servidor'
            ], 500);
        }
    }
}