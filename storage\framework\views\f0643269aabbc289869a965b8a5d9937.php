<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'prefixActions' => [],
    'suffixActions' => [],
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'prefixActions' => [],
    'suffixActions' => [],
]); ?>
<?php foreach (array_filter(([
    'prefixActions' => [],
    'suffixActions' => [],
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
    $prefixActions = array_filter(
        $prefixActions,
        fn (\Filament\Infolists\Components\Actions\Action $prefixAction): bool => $prefixAction->isVisible(),
    );

    $suffixActions = array_filter(
        $suffixActions,
        fn (\Filament\Infolists\Components\Actions\Action $suffixAction): bool => $suffixAction->isVisible(),
    );

    $affixActionsClasses = 'flex items-center gap-3 self-stretch';
?>

<div
    <?php echo e($attributes->class(['fi-in-affixes flex'])); ?>

>
    <?php if(count($prefixActions)): ?>
        <div
            class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                $affixActionsClasses,
                'pe-2',
            ]); ?>"
        >
            <?php $__currentLoopData = $prefixActions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $prefixAction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo e($prefixAction); ?>

            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php endif; ?>

    <div class="min-w-0 flex-1">
        <?php echo e($slot); ?>

    </div>

    <?php if(count($suffixActions)): ?>
        <div
            class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                $affixActionsClasses,
                'ps-2',
            ]); ?>"
        >
            <?php $__currentLoopData = $suffixActions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $suffixAction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo e($suffixAction); ?>

            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\xamp8.1\htdocs\vendor\filament\infolists\resources\views\components\affixes.blade.php ENDPATH**/ ?>