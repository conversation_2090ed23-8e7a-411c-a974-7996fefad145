<?php

namespace App\Filament\Admin\Pages;

use App\Models\Gateway;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Exceptions\Halt;

class GatewayPage extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static string $view = 'filament.pages.gateway-page';
    protected static ?string $navigationLabel = 'Gateway de Pagamento';
    protected static ?string $modelLabel = 'Gateway de Pagamento';
    protected static ?string $title = 'Gateway de Pagamento';
    protected static ?string $slug = 'gateway-pagamento';

    public ?array $data = [];
    public Gateway $gateway;

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('admin');
    }

    public function mount(): void
    {
        $gateway = Gateway::first();
        if (!empty($gateway)) {
            $this->gateway = $gateway;
            $this->form->fill($this->gateway->toArray());
        } else {
            $this->form->fill();
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('CONFIGURAÇÃO DO GATEWAY BSPAY')
                ->description('Configure suas chaves de API para o gateway BSPay - Único gateway PIX ativo')
                    ->schema([
                        TextInput::make('bspay_uri')
                            ->label('CLIENTE URL')
                            ->placeholder('Digite a url da api BSPay')
                            ->maxLength(191)
                            ->columnSpanFull(),
                        TextInput::make('bspay_cliente_id')
                            ->label('CLIENTE ID')
                            ->placeholder('Digite o client ID do BSPay')
                            ->maxLength(191)
                            ->columnSpanFull(),
                        TextInput::make('bspay_cliente_secret')
                            ->label('CLIENTE SECRETO')
                            ->placeholder('Digite o client secret do BSPay')
                            ->maxLength(191)
                            ->columnSpanFull(),
                    ])
            ]);
    }

    public function submit(): void
    {
        try {
            if (env('APP_DEMO')) {
                Notification::make()
                    ->title('Atenção')
                    ->body('Você não pode realizar esta alteração na versão demo')
                    ->danger()
                    ->send();
                return;
            }

            $gateway = Gateway::first();
            $data = $this->form->getState();

            if (!empty($gateway)) {
                if ($gateway->update($data)) {
                    Notification::make()
                        ->title('Dados alterados')
                        ->body('Dados alterados com sucesso!')
                        ->success()
                        ->send();
                }
            } else {
                if (Gateway::create($data)) {
                    Notification::make()
                        ->title('Dados criados')
                        ->body('Dados criados com sucesso!')
                        ->success()
                        ->send();
                }
            }

        } catch (Halt $exception) {
            Notification::make()
                ->title('Erro ao alterar dados!')
                ->body('Erro ao alterar dados!')
                ->danger()
                ->send();
        }
    }
}
