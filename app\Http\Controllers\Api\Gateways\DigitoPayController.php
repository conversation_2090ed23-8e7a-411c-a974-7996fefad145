<?php

namespace App\Http\Controllers\Gateway;

use App\Http\Controllers\Controller;
use App\Models\AffiliateWithdraw;
use App\Models\DigitoPayPayment;
use App\Models\Wallet;
use App\Models\Withdrawal;
use App\Traits\Gateways\DigitoPayTrait;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;

class DigitoPayController extends Controller
{
    use DigitoPayTrait;

    /*** @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function callbackMethodPayment(Request $request)
    {
        $data = $request->all();
        \DB::table('debug')->insert(['text' => json_encode($request->all())]);

        return response()->json([], 200);
    }

    /*** @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function callbackMethod(Request $request)
    {
        $data = $request->all();

        self::webhookDigito($request);
    }

    /*** @param Request $request
     * @return null
     */
    public function getQRCodePix(Request $request)
    {
        return self::requestQrcodeDigito($request);
    }

    /*** Show the form for creating a new resource.
     */


    /**vc
     * @param $id
     * @return \Illuminate\Http\RedirectResponse|void
     */
}
