<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Resources\BannerResource\Pages;
use App\Filament\Resources\BannerResource\RelationManagers;
use App\Models\Banner;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Illuminate\Support\HtmlString;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BannerResource extends Resource
{
    protected static ?string $model = Banner::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $label = 'Banner';
    protected static ?string $pluralLabel = 'ADICIONAR BANNERS NA PÁGINA INICIAL';

    protected static ?string $navigationLabel = 'DEFINIÇÕES DE BANNERS';

    protected static ?string $navigationGroup = 'Marketing';  // Opcional: para agrupar em uma seção de navegação

    /**
     * @dev 𝓗𝓐𝓡𝓚𝓩𝓘𝓜 / by kesung.fun < - Sistema otimizado
     * @return bool
     */
    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('admin');
    }

    /**
     * @param Form $form
     * @return Form
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('ONDA GAMES CRIOU ESSA PLATAFORMA PARA VOCÊ')
                ->description(new HtmlString('
                    <div style="font-weight: 600; display: flex; align-items: center;">
                        SAIBA MAIS SOBRE NÓS. PARTICIPE DA NOSSA COMUNIDADE IGAMING. ACESSE AGORA!
                        <a class="dark:text-white"
                           style="
                                font-size: 14px;
                                font-weight: 600;
                                width: 127px;
                                display: flex;
                                background-color: #f800ff;
                                padding: 10px;
                                border-radius: 11px;
                                justify-content: center;
                                margin-left: 10px;
                           "
                           href="https://ondagames.com"
                           target="_blank">
                            SITE OFICIAL
                        </a>
                        <a class="dark:text-white"
                           style="
                                font-size: 14px;
                                font-weight: 600;
                                width: 127px;
                                display: flex;
                                background-color: #f800ff;
                                padding: 10px;
                                border-radius: 11px;
                                justify-content: center;
                                margin-left: 10px;
                           "
                           href="https://t.me/ondagames01"
                           target="_blank">
                            GRUPO TELEGRAM
                        </a>
                    </div>
                ')),
                Forms\Components\Section::make()
                    ->description('')
                    ->schema([
                        Forms\Components\Group::make()->schema([
                            Forms\Components\Group::make()
                            ->schema([
                                Forms\Components\TextInput::make('link')
                                    ->label('LINK DO BANNER')
                                    ->placeholder('Digite o link do banner')
                                    ->maxLength(191),
                                Forms\Components\Select::make('type')
                                    ->label('TIPAGEM DO BANNER')
                                    ->options([
                                        'carousel' => 'BANNER CARROSSEL',
                                        'home' => 'PROPAGANDA DA HOME',
                                    ])
                                    ->required(),
                            ])->columns(2)->columnSpanFull(),

                            Forms\Components\Textarea::make('description')
                                ->label('DESCRICÃO DO BANNER')
                                ->placeholder('Digite uma descrição')
                                ->maxLength(65535)
                                ->columnSpanFull(),
                        ])->columns(2),
                        Forms\Components\FileUpload::make('image')
                            ->label('IMAGEM DO BANNER')
                            ->image()
                            ->required(),
                    ])
            ]);
    }

    /**
     * @param Table $table
     * @return Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->label('Imagem'),
                Tables\Columns\TextColumn::make('link')
                    ->label('Link'),
                Tables\Columns\TextColumn::make('type')
                    ->label('Tipo'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \App\Filament\Admin\Resources\BannerResource\Pages\ListBanners::route('/'),
            'create' => \App\Filament\Admin\Resources\BannerResource\Pages\CreateBanner::route('/create'),
            'edit' => \App\Filament\Admin\Resources\BannerResource\Pages\EditBanner::route('/{record}/edit'),
        ];
    }
}
